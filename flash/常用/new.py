from flask import Flask, render_template_string, request, session
import os
import numpy as np
import logging
from datetime import datetime
from collections import deque

# 定义库存处理类
class InventoryProcessor:
    def __init__(self, threshold=None, highlight_num=40, is_ph_file=False, is_ma=False):
        # 统一阈值设置
        self.threshold = threshold if threshold is not None else (500 if is_ma else 270 if is_ph_file else 190)
        self.highlight_num = highlight_num
        self.is_ph_file = is_ph_file
        self.is_ma = is_ma

    @staticmethod
    def parse_time(time_str):
        time_str = time_str.strip().rstrip(':')
        for fmt in ["%Y-%m-%d %H:%M:%S", "%Y-%m-%d %H:%M"]:
            try:
                return datetime.strptime(time_str, fmt)
            except ValueError:
                continue
        logging.error(f"无法解析日期时间字符串: {time_str}")
        return datetime.min

    def process_queue(self, raw_value, pending, valid, time_window, current_ts):
        processed_value = int(round(raw_value))
        if processed_value < 0:
            qty = abs(processed_value)
            sale_record = {
                'timestamp': current_ts,
                'original': qty,
                'remaining': qty,
                'valid': qty <= self.threshold
            }
            pending.append(sale_record)
            if sale_record['valid']:
                valid.append(qty)
            return qty
        else:
            refund = abs(processed_value)
            for i in range(len(pending) - 1, -1, -1):
                record = pending[i]
                time_diff = (self.parse_time(current_ts) - self.parse_time(record['timestamp'])).total_seconds()
                if time_diff <= time_window and record['valid'] and record['original'] == refund:
                    try:
                        valid.remove(refund)
                    except ValueError:
                        pass
                    del pending[i]
                    return refund
            return 0

    def process_data(self, data):
        # 如果数据为空，直接返回空结果
        if not data:
            return [], [], 0, 0

        processed_data = []
        old_app_pending = deque(maxlen=100)
        shop_pending = deque(maxlen=100)
        new_app_pending = deque(maxlen=100)


        old_app_valid = []
        shop_valid = []
        new_app_valid = []

        total_sales = 0
        app_sales = 0

        # 预先分配足够的容量
        processed_data = [None] * len(data)
        idx = 0

        for line in data:
            if not line.strip():
                continue

            parts = line.split(',')
            if len(parts) < 5:
                continue

            try:
                ts = parts[0].strip()
                raw_sold = float(parts[2])
                raw_stock = float(parts[4])
                raw_new_app_change = 0.0
                if len(parts) >= 7:
                    raw_new_app_change = float(parts[6])

                # 筛选并计算总销量
                if abs(raw_sold) < self.threshold:
                    total_sales += -raw_sold
                if abs(raw_stock) < self.threshold:
                    total_sales += -raw_stock
                if abs(raw_new_app_change) < self.threshold:
                    total_sales += -raw_new_app_change


                # 计算 app 端的销量（旧版app + 新版app）
                if abs(raw_sold) <= self.threshold:
                    if raw_sold < 0:
                        app_sales += abs(raw_sold)
                    else:
                        app_sales -= raw_sold
                
                if abs(raw_new_app_change) <= self.threshold:
                    if raw_new_app_change < 0:
                        app_sales += abs(raw_new_app_change)
                    else:
                        app_sales -= raw_new_app_change

                self.process_queue(raw_sold, old_app_pending, old_app_valid, 1980, ts)
                self.process_queue(raw_stock, shop_pending, shop_valid, 1020, ts)
                if len(parts) >= 7:
                    self.process_queue(raw_new_app_change, new_app_pending, new_app_valid, 1020, ts)
                
                filtered = abs(raw_sold) > self.threshold or abs(raw_stock) > self.threshold or abs(raw_new_app_change) > self.threshold

                # 直接在指定位置存储，避免append的开销
                processed_data[idx] = (line, filtered, raw_sold, raw_stock, raw_new_app_change)
                idx += 1

            except Exception as e:
                logging.error(f"数据处理失败: {line} - {str(e)}")
                continue

        # 删除未使用的元素
        processed_data = processed_data[:idx]

        # 保持时间降序排列
        processed_data.reverse()

        # 高效合并并排序
        combined = sorted(old_app_valid + shop_valid + new_app_valid, reverse=True)
        if self.highlight_num < len(combined):
            combined = combined[:self.highlight_num]

        return processed_data, combined, total_sales, app_sales

    @staticmethod
    def calculate_stats(values):
        if not values:
            return {'min': 0, 'max': 0, 'median': 0, 'mean': 0}
        arr = np.array(values)
        return {
            'min': int(np.min(arr).item()),
            'max': int(np.max(arr).item()),
            'median': float(np.median(arr).item()),
            'mean': round(float(np.mean(arr)), 2)
        }

# 配置日志记录
logging.basicConfig(level=logging.INFO)
app = Flask(__name__)
app.secret_key = 'your_secure_secret_key_here'  # 请替换为安全的密钥
app.config['SEND_FILE_MAX_AGE_DEFAULT'] = 86400  # 静态资源缓存1天

@app.context_processor
def utility_processor():
    return dict(abs=abs)


def get_available_files():
    file_paths = {}
    current_dir = os.getcwd()  # 获取当前工作目录

    # 遍历当前目录下的所有文件
    for file in os.listdir(current_dir):
        if file.endswith('.txt'):
            # 使用文件名(不带扩展名)作为键
            key = os.path.splitext(file)[0]
            file_paths[key] = file

    return file_paths

# 获取可用的文件列表
def get_available_buttons():
    return list(get_available_files().keys())

# 使用动态文件发现替代静态文件路径字典

# file_paths = {
#     'yuqi':'yuqi.txt',
#     'miyeon':'miyeon.txt',
#     'minnie':'minnie.txt',
#     'shuhua':'shuhua.txt',
#     'soyeon':'soyeon.txt',
#     'superjunior':'superjunior.txt',
#     'wayv':'wayv.txt',
#     'phdonghai':'phdonghai.txt',
#     'phguixian':'phguixian.txt',
#     'phlite':'phlite.txt',
#     'phlixu':'phlixu.txt',
#     'phshiyuan':'phshiyuan.txt',
#     'phxice':'phxice.txt',
#     'phyinhe':'phyinhe.txt',
#     'phyisheng':'phyisheng.txt',
#     'irene':'irene.txt',
#     'seulgi':'seulgi.txt',
#     'phirene':'phirene.txt',
#     'phseulgi':'phseulgi.txt',
#     'alldayproject':'alldayproject.txt',
#     'kickflip':'kickflip.txt',
#     'doyoung':'doyoung.txt',
#     'allen':'allen.txt',
#     'hyeonjun':'hyeongjun.txt',
#     'jungmo':'jungmo.txt',
#     'seongmin':'seongmin.txt',
#     'serim':'serim.txt',
#     'minhee':'minhee.txt',
#     'taeyoung':'taeyoung.txt',
#     'wonjin':'wonjin.txt',
#     'woobin':'woobin.txt'
# }

template = '''
<html>
<head>
    <title>圆梦大使数据网站，请勿外传</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <!-- 引入Excel导出库 -->
    <script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
    <style>
        :root {
            --primary-color: #4CAF50;
            --secondary-color: #f8f9fa;
            --text-color: #212529;
        }
        
        * {
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            margin: 8px;
            color: var(--text-color);
            line-height: 1.5;
        }

        .container {
            max-width: 100%;
            overflow-x: auto;
        }

        .data-header {
            text-align: center;
            margin: 12px 0;
            font-size: 1.5rem;
            color: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .button-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(90px, 1fr));
            gap: 5px;
            margin: 12px 0;
        }

        button {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 8px;
            border-radius: 5px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: opacity 0.2s;
        }

        button:active {
            opacity: 0.8;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 12px 0;
            font-size: 0.78rem;
            table-layout: fixed;
        }

        th, td {
            padding: 8px;
            text-align: center;
            border: 1px solid #dee2e6;
            word-break: break-all;
        }

        th {
            background-color: var(--secondary-color);
            font-weight: 600;
            font-size: 0.8rem;
        }

        /* 颜色定义 */
        .positive-change {
            background-color: #ffebee;  /* 库存回退-浅红色 */
        }
        .unfiltered {
            background-color: #e8f5e9;  /* 有效未过滤-浅黄色 */
        }
        .filtered {
            background-color: #ffffe0;  /* 正常/注水数据-浅绿色 */
        }

        .stats-box {
            background: var(--secondary-color);
            border-radius: 6px;
            padding: 10px;
            margin: 10px 0;
            font-size: 0.85rem;
        }

        .highlight-section {
            margin: 12px 0;
            padding: 8px;
            background: #f3f4f6;
            border-radius: 6px;
        }

        .highlight-group {
            margin-top: 6px;
        }

        .highlight-block {
            margin: 6px 0;
        }

        .highlight-block h4 {
            margin: 4px 0;
            color: #666;
            font-size: 0.78rem;
        }

        .highlight-row {
            display: flex;
            flex-wrap: wrap;
            gap: 2px;
            margin: 3px 0;
        }

        .highlight-item {
            flex: 0 0 calc(6.66% - 2px);
            text-align: center;
            padding: 2px;
            background: #e3f2fd;
            border-radius: 2px;
            font-weight: 500;
            font-size: 0.68rem;
            min-width: 20px;
            line-height: 1.2;
        }

        input[type="number"] {
            padding: 6px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 90px;
            margin: 0 3px;
            font-size: 0.8rem;
        }

        .form-group {
            margin: 6px 0;
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            align-items: center;
        }
        
        /* 弹窗样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background-color: white;
            margin: 15% auto;
            padding: 20px;
            border-radius: 8px;
            width: 80%;
            max-width: 600px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .close:hover {
            color: black;
        }
        
        .help-button {
            background: #2196F3;
            margin-left: 5px;
        }
        
        /* 有颜色的文字加深颜色 */
        .text-green {
            color: #006400;
            font-weight: 500;
        }
        
        .text-red {
            color: #8B0000;
            font-weight: 500;
        }
        
        .text-yellow {
            color: #8B8000;
            font-weight: 500;
        }
        
        /* 自动刷新选项样式 */
        .auto-refresh {
            display: flex;
            align-items: center;
            background: #e8f5e9;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 0.85rem;
            margin-left: 10px;
        }
        
        .auto-refresh-inline {
            display: flex;
            align-items: center;
            padding: 0 5px;
            border-radius: 4px;
            font-size: 0.75rem;
            margin-left: 5px;
            background: #e8f5e9;
            height: 100%;
            white-space: nowrap;
        }
        
        .export-btn {
            background-color: #3f51b5;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 6px 10px;
            font-size: 0.75rem;
            cursor: pointer;
            margin-left: 5px;
            white-space: nowrap;
            box-shadow: 0 1px 3px rgba(0,0,0,0.2);
            transition: background-color 0.3s;
        }
        
        .export-btn:hover {
            background-color: #303f9f;
        }
        
        .auto-refresh input, .auto-refresh-inline input {
            margin-right: 5px;
        }
        
        .tooltip {
            position: relative;
            display: inline-block;
            margin-left: 5px;
        }

        .tooltip .tooltiptext {
            visibility: hidden;
            width: 200px;
            background-color: #555;
            color: #fff;
            text-align: center;
            border-radius: 6px;
            padding: 5px;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            margin-left: -100px;
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 0.75rem;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }

        .tooltip:hover .tooltiptext {
            visibility: visible;
            opacity: 1;
        }

        @media (max-width: 480px) {
            table {
                font-size: 0.7rem;
            }

            th, td {
                padding: 4px;
                font-size: 0.65rem;
                white-space: normal;
            }
            
            button {
                padding: 6px;
                font-size: 0.72rem;
            }
            
            .data-header {
                font-size: 1.3rem;
                flex-wrap: wrap;
            }

            .highlight-item {
                flex: 0 0 calc(10% - 2px);
                font-size: 0.6rem;
                min-width: 18px;
            }
            
            .highlight-block h4 {
                font-size: 0.7rem;
            }

            td:first-child {
                min-width: 50px;
                max-width: 60px;
            }
            
            .modal-content {
                margin: 30% auto;
                width: 90%;
                padding: 15px;
            }
            
            .auto-refresh-inline {
                font-size: 0.65rem;
                padding: 0 3px;
                margin-left: 3px;
            }
            
            .export-btn {
                font-size: 0.65rem;
                padding: 4px 6px;
                margin-left: 3px;
            }
            
            /* 优化移动端数据面板 */
            .data-dashboard {
                padding: 6px;
                margin: 6px 0;
            }
            
            .dashboard-stats {
                grid-template-columns: repeat(3, 1fr);
                gap: 2px;
                font-size: 0.6rem;
                margin-bottom: 5px;
            }
            
            .stat-item {
                padding: 1px 2px;
            }
            
            .highlight-stats {
                gap: 2px;
                font-size: 0.6rem;
            }
            
            .dashboard-header h3 {
                font-size: 0.8rem;
                margin-bottom: 3px;
            }
        }

        @media (max-width: 360px) {
            th, td {
                padding: 3px;
                font-size: 0.6rem;
            }

            .highlight-item {
                flex: 0 0 calc(12% - 2px);
                font-size: 0.56rem;
            }

            input[type="number"] {
                width: 80px;
                padding: 5px;
            }
        }

        .stats-box {
            background: var(--secondary-color);
            border-radius: 6px;
            padding: 10px;
            margin: 10px 0;
            font-size: 0.85rem;
        }

        .highlight-section {
            margin: 12px 0;
            padding: 8px;
            background: #f3f4f6;
            border-radius: 6px;
        }

        .highlight-group {
            margin-top: 6px;
        }

        .highlight-block {
            margin: 6px 0;
        }

        .highlight-block h4 {
            margin: 4px 0;
            color: #666;
            font-size: 0.78rem;
        }

        .highlight-row {
            display: flex;
            flex-wrap: wrap;
            gap: 2px;
            margin: 3px 0;
        }

        .highlight-item {
            flex: 0 0 calc(6.66% - 2px);
            text-align: center;
            padding: 2px;
            background: #e3f2fd;
            border-radius: 2px;
            font-weight: 500;
            font-size: 0.68rem;
            min-width: 20px;
            line-height: 1.2;
        }
        
        /* 组合数据区域 */
        .data-dashboard {
            background: var(--secondary-color);
            border-radius: 6px;
            padding: 10px;
            margin: 10px 0;
        }
        
        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;
            flex-wrap: wrap;
        }
        
        .dashboard-header h3 {
            margin: 0;
            font-size: 0.95rem;
            color: var(--primary-color);
        }
        
        .dashboard-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 5px;
            margin-bottom: 8px;
            font-size: 0.85rem;
        }
        
        .stat-item {
            padding: 2px 4px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            text-align: center;
        }
        
        .dashboard-highlights {
            margin-top: 5px;
        }
        
        .highlight-header {
            font-size: 0.9rem;
            margin: 5px 0;
            color: #666;
        }
        
        .highlight-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 5px;
            margin: 5px 0;
            font-size: 0.78rem;
        }
    </style>
</head>
<body>
    <div class="data-header">
        <span>🐾 圆梦大使签售表格 - {{ file_name|upper }} 🐾</span>
    </div>
    
    <!-- 隐藏字段存储当前文件名 -->
    <input type="hidden" id="currentFileName" value="{{ file_name }}">
    
    {% if show_buttons %}
    <form method="post">
        <div class="button-group">
            {% for name in buttons %}
            <button type="submit" name="{{ name }}">{{ name|upper }}</button>
            {% endfor %}
            <button type="button" class="help-button" id="helpButton">使用说明</button>
        </div>
    </form>
    {% endif %}

    <!-- 使用说明弹窗 -->
    <div id="helpModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h3>📌 使用说明</h3>
            <p>⏱️ 网站实时更新，点击绿色成员按键会刷新一次，数据标<span class="text-green">绿色</span>则为正常数据，数据标<span class="text-red">红色</span>则为库存退回数据，数据标<span class="text-yellow">黄色</span>则为注水数据，因为kms会有智能注水，所以会多拉一些高光数来判断；</p>
            <p>⚙️ 注水范围以及显示个数可以通过填空来设置，如果不会就不需要填空，默认我的选择；</p>
            <p>⚠️ 求稳建议下到阶段的中位数以上；</p>
            <p>🔍 影通一般，总销量÷（抽选人数+10）附近波动，线下签售按照抽选人数进行判断，经供参考；</p>
            <p>🔄 勾选<strong>自动刷新</strong>选项可以让页面每秒自动刷新一次，获取最新数据！</p>
        </div>
    </div>

    <div class="container">
        <form method="post">
            <div class="form-group">
                <input type="number" name="threshold" placeholder="注水数据范围" step="1" min="0">
                <input type="number" name="highlight_num" placeholder="显示数量个数" min="1">
                <button type="submit">设置</button>
            </div>
            <div class="form-group">
                <input type="number" name="cut_value" placeholder="总销÷" step="0.1" min="0">
                <button type="submit">计算</button>
                <div class="auto-refresh-inline">
                    <input type="checkbox" id="autoRefresh"> 
                    <label for="autoRefresh">自动刷新</label>
                    <div class="tooltip">ℹ️
                        <span class="tooltiptext">勾选后每3秒自动刷新页面，取消勾选停止刷新</span>
                    </div>
                </div>
                <button type="button" id="exportExcel" class="export-btn"></button>
            </div>
        </form>

        <div class="data-dashboard">
            <div class="dashboard-header">
                <h3>📊 数据统计 & 🌟 高光数据（去卡单） (TOP {{ highlight_num }})</h3>
            </div>
            
            <div class="dashboard-stats">
                {% if total is not none %}
                <div class="stat-item">🔢 总销: {{ total }}</div>
                <div class="stat-item">📱 APP销量: {{ kms_total }}</div>
                {% if stats is not none %}
                <div class="stat-item">⬇️ 最小值: {{ stats.min }}</div>
                {% endif %}
                {% endif %}
                
                {% if stats is not none %}
                <div class="stat-item">⬆️ 最大值: {{ stats.max }}</div>
                <div class="stat-item">📏 中位数: {{ stats.median }}</div>
                <div class="stat-item">📊 平均数: {{ stats.mean }}</div>
                {% endif %}
                
                {% if cut is not none %}
                <div class="stat-item">🧮 计算值: {{ cut }}</div>
                {% endif %}
                
                {% if file_name == 'ive' %}
                <div class="dashboard-stats">
                    <div class="stat-item">1-50最小: {{ stats_1_50.min }}</div>
                    <div class="stat-item">最大: {{ stats_1_50.max }}</div>
                    <div class="stat-item">中位: {{ stats_1_50.median }}</div>
                    
                    <div class="stat-item">51-200最小: {{ stats_51_200.min }}</div>
                    <div class="stat-item">最大: {{ stats_51_200.max }}</div>
                    <div class="stat-item">中位: {{ stats_51_200.median }}</div>
                </div>
                {% endif %}
                
                {% if file_name == '' %}
                <div class="dashboard-stats">
                    <div class="stat-item">1-50最小: {{ stats_1_50.min }}</div>
                    <div class="stat-item">最大: {{ stats_1_50.max }}</div>
                    <div class="stat-item">中位: {{ stats_1_50.median }}</div>
                    
                    <div class="stat-item">51-200最小: {{ stats_51_250.min }}</div>
                    <div class="stat-item">最大: {{ stats_51_250.max }}</div>
                    <div class="stat-item">中位: {{ stats_51_250.median }}</div>
                </div>
                {% endif %}
            </div>
            
            <div class="dashboard-highlights">
                <div class="highlight-group">
                {% if file_name =='' %}
                    {% if stats_1_50 is not none %}
                    <div class="highlight-block">
                        <h4>1 - 50:</h4>
                        <div class="highlight-row">
                            {% for value in highlighted_values[:50] if highlighted_values is not none %}
                            <span class="highlight-item">{{ value }}</span>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                    {% if stats_51_200 is not none %}
                    <div class="highlight-block">
                        <h4>51 - 200:</h4>
                        <div class="highlight-row">
                            {% for value in highlighted_values[50:200] if highlighted_values is not none %}
                            <span class="highlight-item">{{ value }}</span>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                {% elif file_name in ['yy','kun','xj','ten','hen','renjun','jisung','jaemin','jeno','mark','haechan','chenle','soobin', 'yeonjun', 'beomgyu', 'taehyun', 'huenigkai','seulgi','irene','alldayproject','coffee','closeyoureye'] %}
                    {% if stats_1_25 is not none %}
                    <div class="highlight-block">
                        <h4>1 - 15:</h4>
                        <h4>最小 {{ stats_1_25.min }} | 最大 {{ stats_1_25.max }} | 中位 {{ stats_1_25.median }} | 平均 {{ stats_1_25.mean }}</h4>
                        <div class="highlight-row">
                            {% for value in highlighted_values[:15] if highlighted_values is not none %}
                            <span class="highlight-item">{{ value }}</span>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                    {% if stats_26_40 is not none %}
                    <div class="highlight-block">
                        <h4>16 - 30:</h4>
                        <h4>最小 {{ stats_26_40.min }} | 最大 {{ stats_26_40.max }} | 中位 {{ stats_26_40.median }} | 平均 {{ stats_26_40.mean }}</h4>
                        <div class="highlight-row">
                            {% for value in highlighted_values[15:30] if highlighted_values is not none %}
                            <span class="highlight-item">{{ value }}</span>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                {% elif file_name == 'h2hsign' and start == 160 and end == 240 %}
                    {% if stats_160_220 is not none %}
                    <div class="highlight-block">
                        <h4>161 - 220:</h4>
                        <div class="highlight-row">
                            {% for value in highlighted_values[161:220] if highlighted_values is not none %}
                            <span class="highlight-item">{{ value }}</span>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                {% elif file_name == 'wayv' %}
                    {% if stats_1_125 is not none %}
                    <div class="highlight-block">
                        <h4>1 - 125:</h4>
                        <h4>最小 {{ stats_1_125.min }} | 最大 {{ stats_1_125.max }} | 中位 {{ stats_1_125.median }} | 平均 {{ stats_1_125.mean }}</h4>
                        <div class="highlight-row">
                            {% for value in highlighted_values[:125] if highlighted_values is not none %}
                            <span class="highlight-item">{{ value }}</span>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                    {% if stats_126_225 is not none %}
                    <div class="highlight-block">
                        <h4>126 - 225:</h4>
                        <h4>最小 {{ stats_126_225.min }} | 最大 {{ stats_126_225.max }} | 中位 {{ stats_126_225.median }} | 平均 {{ stats_126_225.mean }}</h4>
                        <div class="highlight-row">
                            {% for value in highlighted_values[125:225] if highlighted_values is not none %}
                            <span class="highlight-item">{{ value }}</span>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                {% elif file_name in ['yenasz','yenagz'] %}
                    {% if stats_1_5 is not none %}
                    <div class="highlight-block">
                        <h4>1 - 30:</h4>
                        <h4>最小 {{ stats_1_5.min }} | 最大 {{ stats_1_5.max }} | 中位 {{ stats_1_5.median }} | 平均 {{ stats_1_5.mean }}</h4>
                        <div class="highlight-row">
                            {% for value in highlighted_values[:30] if highlighted_values is not none %}
                            <span class="highlight-item">{{ value }}</span>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                    {% if stats_6_10 is not none %}
                    <div class="highlight-block">
                        <h4>31 - 100:</h4>
                        <h4>最小 {{ stats_6_10.min }} | 最大 {{ stats_6_10.max }} | 中位 {{ stats_6_10.median }} | 平均 {{ stats_6_10.mean }}</h4>
                        <div class="highlight-row">
                            {% for value in highlighted_values[30:100] if highlighted_values is not none %}
                            <span class="highlight-item">{{ value }}</span>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                {% elif file_name == '签名应募' %}
                    {% if stats_1_60 is not none %}
                    <div class="highlight-block">
                        <h4>1 - 50:</h4>
                        <h4>最小 {{ stats_1_60.min }} | 最大 {{ stats_1_60.max }} | 中位 {{ stats_1_60.median }} | 平均 {{ stats_1_60.mean }}</h4>
                        <div class="highlight-row">
                            {% for value in highlighted_values[:50] if highlighted_values is not none %}
                            <span class="highlight-item">{{ value }}</span>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                    {% if stats_61_260 is not none %}
                    <div class="highlight-block">
                        <h4>51 - 200:</h4>
                        <h4>最小 {{ stats_61_260.min }} | 最大 {{ stats_61_260.max }} | 中位 {{ stats_61_260.median }} | 平均 {{ stats_61_260.mean }}</h4>
                        <div class="highlight-row">
                            {% for value in highlighted_values[50:200] if highlighted_values is not none %}
                            <span class="highlight-item">{{ value }}</span>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                {% elif file_name in ['合影应募','smn','allen', 'hyeongjun', 'jungmo', 'minhee', 'seongmin', 'serim', 'taeyoung', 'wonjin', 'woobin'] %}
                    {% if stats_1_10 is not none %}
                    <div class="highlight-block">
                        <h4>1 - 5:</h4>
                        <h4>最小 {{ stats_1_10.min }} | 最大 {{ stats_1_10.max }} | 中位 {{ stats_1_10.median }} | 平均 {{ stats_1_10.mean }}</h4>
                        <div class="highlight-row">
                            {% for value in highlighted_values[:5] if highlighted_values is not none %}
                            <span class="highlight-item">{{ value }}</span>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                    {% if stats_15_20 is not none %}
                    <div class="highlight-block">
                        <h4>6 - 10:</h4>
                        <h4>最小 {{ stats_15_20.min }} | 最大 {{ stats_15_20.max }} | 中位 {{ stats_15_20.median }} | 平均 {{ stats_15_20.mean }}</h4>
                        <div class="highlight-row">
                            {% for value in highlighted_values[5:10] if highlighted_values is not none %}
                            <span class="highlight-item">{{ value }}</span>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                {% elif file_name in ['single', '2'] %}
                    {% if stats_1_50 is not none %}
                    <div class="highlight-block">
                        <h4>1 - 40:</h4>
                        <h4>最小 {{ stats_1_50.min }} | 最大 {{ stats_1_50.max }} | 中位 {{ stats_1_50.median }} | 平均 {{ stats_1_50.mean }}</h4>
                        <div class="highlight-row">
                            {% for value in highlighted_values[:40] if highlighted_values is not none %}
                            <span class="highlight-item">{{ value }}</span>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                    {% if stats_51_190 is not none %}
                    <div class="highlight-block">
                        <h4>41 - 80:</h4>
                        <h4>最小 {{ stats_51_190.min }} | 最大 {{ stats_51_190.max }} | 中位 {{ stats_51_190.median }} | 平均 {{ stats_51_190.mean }}</h4>
                        <div class="highlight-row">
                            {% for value in highlighted_values[40:80] if highlighted_values is not none %}
                            <span class="highlight-item">{{ value }}</span>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                {% elif file_name in ['kickflip', 'ahof'] %}
                    {% if stats_1_50 is not none %}
                    <div class="highlight-block">
                        <h4>1 - 50:</h4>
                        <h4>最小 {{ stats_1_50.min }} | 最大 {{ stats_1_50.max }} | 中位 {{ stats_1_50.median }} | 平均 {{ stats_1_50.mean }}</h4>
                        <div class="highlight-row">
                            {% for value in highlighted_values[:50] if highlighted_values is not none %}
                            <span class="highlight-item">{{ value }}</span>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                    <div class="highlight-block">
                        <h4>51 - 150:</h4>
                          <h4>最小 {{ stats_51_125.min }} | 最大 {{ stats_51_125.max }} | 中位 {{ stats_51_125.median }} | 平均 {{ stats_51_125.mean }}</h4>
                        <div class="highlight-row">
                            {% for value in highlighted_values[50:150] if highlighted_values is not none %}
                            <span class="highlight-item">{{ value }}</span>
                            {% endfor %}
                        </div>
                    </div>
                {% elif file_name in [ 'shuhua','miyeon','yuqi','minnie','soyeon'] %}
                    <div class="highlight-block">
                        <h4>1 - 5:</h4>
                        <h4>最小 {{ stats_1_25.min }} | 最大 {{ stats_1_25.max }} | 中位 {{ stats_1_25.median }} | 平均 {{ stats_1_25.mean }}</h4>
                        <div class="highlight-row">
                            {% for value in highlighted_values[:5] if highlighted_values is not none %}
                            <span class="highlight-item">{{ value }}</span>
                            {% endfor %}
                        </div>
                    </div>
                    <div class="highlight-block">
                        <h4>6 - 10:</h4>
                        <h4>最小 {{ stats_26_50.min }} | 最大 {{ stats_26_50.max }} | 中位 {{ stats_26_50.median }} | 平均 {{ stats_26_50.mean }}</h4>
                        <div class="highlight-row">
                            {% for value in highlighted_values[6:10] if highlighted_values is not none %}
                            <span class="highlight-item">{{ value }}</span>
                            {% endfor %}
                        </div>
                    </div>
                {% elif file_name in [] %}
                    <div class="highlight-block">
                        <h4>1 -10:</h4>
                        <div class="highlight-row">
                            {% for value in highlighted_values[:10]if highlighted_values is not none %}
                            <span class="highlight-item">{{ value }}</span>
                            {% endfor %}
                        </div>
                    </div>
                {% elif file_name == 'all' %}
                    {% if stats_176_275 is not none %}
                    <div class="highlight-block">
                        <h4>176 - 275:</h4>
                        <h4>最小 {{ stats_176_275.min }} | 最大 {{ stats_176_275.max }} | 中位 {{ stats_176_275.median }} | 平均 {{ stats_176_275.mean }}</h4>
                        <div class="highlight-row">
                            {% for value in highlighted_values[175:275] if highlighted_values is not none %}
                            <span class="highlight-item">{{ value }}</span>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                {% elif file_name in ['sakuya', 'riku', 'yushi', 'jaehee', 'ryo', 'sion'] %}
                    <div class="highlight-block">
                        <h4>1 - 25:</h4>
                        <h4>最小 {{ stats_1_25.min }} | 最大 {{ stats_1_25.max }} | 中位 {{ stats_1_25.median }} | 平均 {{ stats_1_25.mean }}</h4>
                        <div class="highlight-row">
                            {% for value in highlighted_values[:25] if highlighted_values is not none %}
                            <span class="highlight-item">{{ value }}</span>
                            {% endfor %}
                        </div>
                    </div>
                    <div class="highlight-block">
                        <h4>26 - 40:</h4>
                        <h4>最小 {{ stats_26_40.min }} | 最大 {{ stats_26_40.max }} | 中位 {{ stats_26_40.median }} | 平均 {{ stats_26_40.mean }}</h4>
                        <div class="highlight-row">
                            {% for value in highlighted_values[25:40] if highlighted_values is not none %}
                            <span class="highlight-item">{{ value }}</span>
                            {% endfor %}
                        </div>
                    </div>
                    </div>
                {% elif file_name in ['anton', 'eunseok', 'shotaro', 'sohee', 'sungchan', 'wonbin','mark'] %}
                    <div class="highlight-block">
                        <h4>1 - 20:</h4>
                        <h4>最小 {{ stats_1_15.min }} | 最大 {{ stats_1_15.max }} | 中位 {{ stats_1_15.median }} | 平均 {{ stats_1_15.mean }}</h4>
                        <div class="highlight-row">
                            {% for value in highlighted_values[:20] if highlighted_values is not none %}
                            <span class="highlight-item">{{ value }}</span>
                            {% endfor %}
                        </div>
                    </div>
                    <div class="highlight-block">
                        <h4>21 - 40:</h4>
                        <h4>最小 {{ stats_16_30.min }} | 最大 {{ stats_16_30.max }} | 中位 {{ stats_16_30.median }} | 平均 {{ stats_16_30.mean }}</h4>
                        <div class="highlight-row">
                            {% for value in highlighted_values[20:40] if highlighted_values is not none %}
                            <span class="highlight-item">{{ value }}</span>
                            {% endfor %}
                        </div>
                    </div>
                {% elif 'ph' in file_name %}
                    <div class="highlight-block">
                        <h4>1 - 5:</h4>
                        <h4>最小 {{ stats_1_6.min }} | 最大 {{ stats_1_6.max }} | 中位 {{ stats_1_6.median }} | 平均 {{ stats_1_6.mean }}</h4>
                        <div class="highlight-row">
                            {% for value in highlighted_values[:5] if highlighted_values is not none %}
                            <span class="highlight-item">{{ value }}</span>
                            {% endfor %}
                        </div>
                    </div>
                    <div class="highlight-block">
                        <h4>6 - 10:</h4>
                        <h4>最小 {{ stats_7_12.min }} | 最大 {{ stats_7_12.max }} | 中位 {{ stats_7_12.median }} | 平均 {{ stats_7_12.mean }}</h4>
                        <div class="highlight-row">
                            {% for value in highlighted_values[5:10] if highlighted_values is not none %}
                            <span class="highlight-item">{{ value }}</span>
                            {% endfor %}
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
        </div>
        <table>
            <thead>
                <tr>
                    <th style="width: 33%">变动时间</th>
                    <th style="width: 33%">变动渠道</th>
                    <th style="width: 34%">变动数量</th>
                </tr>
            </thead>
            <tbody>
                {% for line, filtered, real_sold, real_stock, real_new_app_change in data %}
                {% set parts = line.split(',') %}
                {% if real_sold != 0 %}
                <tr class="{% if real_sold > 0 %}positive-change{% elif not filtered %}unfiltered{% else %}filtered{% endif %}">
                    <td>{{ parts[0] }}</td>
                    <td>旧版app</td>
                    <td>{{ parts[2] }}</td>
                </tr>
                {% endif %}
                {% if real_stock != 0 %}
                <tr class="{% if real_stock > 0 %}positive-change{% elif not filtered %}unfiltered{% else %}filtered{% endif %}">
                    <td>{{ parts[0] }}</td>
                    <td>微店</td>
                    <td>{{ parts[4] }}</td>
                </tr>
                {% endif %}
                {% if parts|length >= 7 and real_new_app_change != 0 %}
                <tr class="{% if real_new_app_change > 0 %}positive-change{% elif not filtered %}unfiltered{% else %}filtered{% endif %}">
                    <td>{{ parts[0] }}</td>
                    <td>新版app</td>
                    <td>{{ parts[6] }}</td>
                </tr>
                {% endif %}
                {% endfor %}
            </tbody>
        </table>
    </div>

    <script>
        // 弹窗脚本
        document.addEventListener('DOMContentLoaded', function() {
            var modal = document.getElementById('helpModal');
            var btn = document.getElementById('helpButton');
            var span = document.getElementsByClassName('close')[0];
            
            btn.onclick = function() {
                modal.style.display = "block";
            }
            
            span.onclick = function() {
                modal.style.display = "none";
            }
            
            window.onclick = function(event) {
                if (event.target == modal) {
                    modal.style.display = "none";
                }
            }
            
            // 自动刷新功能
            var autoRefreshCheckbox = document.getElementById('autoRefresh');
            var refreshIntervalId = null;
            
            // 确保autoRefresh元素存在
            if (autoRefreshCheckbox) {
                // 获取当前路由的URL路径
                var currentRoute = window.location.pathname;
                var storageKey = 'autoRefreshEnabled_' + currentRoute;
                
                // 从本地存储中恢复当前路由的自动刷新状态
                var savedState = localStorage.getItem(storageKey);
                if (savedState === 'true') {
                    autoRefreshCheckbox.checked = true;
                    startAutoRefresh();
                }
                
                // 监听复选框状态变化
                autoRefreshCheckbox.addEventListener('change', function() {
                    if (this.checked) {
                        startAutoRefresh();
                        localStorage.setItem(storageKey, 'true');
                    } else {
                        stopAutoRefresh();
                        localStorage.setItem(storageKey, 'false');
                    }
                });
                
                // 确保页面卸载时清除定时器
                window.addEventListener('beforeunload', function() {
                    if (refreshIntervalId !== null) {
                        clearInterval(refreshIntervalId);
                    }
                    // 如果复选框是选中状态，保存到localStorage
                    localStorage.setItem(storageKey, autoRefreshCheckbox.checked ? 'true' : 'false');
                });
            }
            
            // 开始自动刷新
            function startAutoRefresh() {
                // 确保没有重复的定时器
                if (refreshIntervalId !== null) {
                    clearInterval(refreshIntervalId);
                }
                
                // 获取当前文件名，确保刷新的是当前阅览文件
                var currentFileName = document.getElementById('currentFileName').value;
                
                refreshIntervalId = setInterval(function() {
                    // 查找当前文件名对应的按钮并点击
                    var buttons = document.querySelectorAll('form button[type="submit"]');
                    var currentButton = null;
                    
                    // 寻找匹配当前文件名的按钮
                    for (var i = 0; i < buttons.length; i++) {
                        if (buttons[i].name === currentFileName) {
                            currentButton = buttons[i];
                            break;
                        }
                    }
                    
                    if (currentButton) {
                        // 模拟点击按钮
                        currentButton.click();
                    } else {
                        // 如果找不到对应按钮，回退到传统刷新方式
                        window.location.reload();
                    }
                }, 3000);
            }
            
            // 停止自动刷新
            function stopAutoRefresh() {
                if (refreshIntervalId !== null) {
                    clearInterval(refreshIntervalId);
                    refreshIntervalId = null;
                }
            }
            
            // 导出Excel功能
            var exportBtn = document.getElementById('exportExcel');
            if (exportBtn) {
                // 设置导出按钮文字
                exportBtn.textContent = "导出Excel";
                exportBtn.addEventListener('click', function() {
                    exportToExcel();
                });
            }
            
            function exportToExcel() {
                try {
                    // 创建一个新的工作簿
                    const wb = XLSX.utils.book_new();
                    
                    // 获取当前文件名
                    const currentFileName = document.getElementById('currentFileName').value || '圆梦大使';
                    
                    // 准备数据
                    // 计算表格数据部分
                    const tableHeaders = Array.from(document.querySelectorAll('table thead th'))
                        .map(th => th.textContent.trim());
                    const tableColCount = tableHeaders.length;
                    
                    const tableRows = [];
                    document.querySelectorAll('table tbody tr').forEach(tr => {
                        const row = Array.from(tr.querySelectorAll('td'))
                            .map(td => td.textContent.trim());
                        tableRows.push(row);
                    });
                    
                    // 统计数据部分
                    let statsData = [];
                    
                    // 添加基本统计数据
                    statsData.push(['📊 数据统计']);
                    statsData.push([]);
                    
                    // 所有统计项目
                    const statItems = document.querySelectorAll('.stat-item');
                    const statsRows = [];
                    statItems.forEach(item => {
                        if (item.textContent.trim()) {
                            statsRows.push([item.textContent.trim()]);
                        }
                    });
                    
                    // 按照每行3项重排统计数据
                    for (let i = 0; i < statsRows.length; i += 3) {
                        const row = [];
                        for (let j = 0; j < 3; j++) {
                            if (i + j < statsRows.length) {
                                row.push(statsRows[i + j][0]);
                            } else {
                                row.push('');
                            }
                        }
                        statsData.push(row);
                    }
                    
                    statsData.push([]);
                    statsData.push(['🌟 高光数据']);
                    statsData.push([]);
                    
                    // 处理高光数据
                    const highlightBlocks = document.querySelectorAll('.highlight-block');
                    
                    // 找到第一个高光块的标题和统计信息
                    if (highlightBlocks.length > 0) {
                        highlightBlocks.forEach(block => {
                            try {
                                const title = block.querySelector('h4:first-child');
                                if (title) {
                                    statsData.push([title.textContent.trim()]);
                                    
                                    const stats = block.querySelector('h4:nth-child(2)');
                                    if (stats) {
                                        statsData.push([stats.textContent.trim()]);
                                    }
                                    
                                    // 获取高光值并按5个一行显示
                                    const values = Array.from(block.querySelectorAll('.highlight-item'))
                                        .map(item => item.textContent.trim());
                                    
                                    for (let i = 0; i < values.length; i += 5) {
                                        const rowValues = values.slice(i, i + 5);
                                        while (rowValues.length < 5) rowValues.push('');
                                        statsData.push(rowValues);
                                    }
                                    
                                    statsData.push([]);
                                }
                            } catch (e) {
                                console.error('处理高光块时出错:', e);
                            }
                        });
                    }
                    
                    // 找出统计数据的最大列数
                    const statsColCount = statsData.reduce((max, row) => Math.max(max, row.length), 0);
                    
                    // 创建完整数据表
                    const aoa = [];
                    
                    // 标题行 - 跨越所有列
                    const totalColumns = tableColCount + 2 + statsColCount;
                    const titleRow = [currentFileName.toUpperCase() + ' 销售数据'];
                    for (let i = 1; i < totalColumns; i++) titleRow.push('');
                    aoa.push(titleRow);
                    
                    // 空行
                    aoa.push(Array(totalColumns).fill(''));
                    
                    // 表头行 - 分为左侧销售明细和右侧统计数据
                    const headerRow = [...tableHeaders];
                    // 添加两个空列作为分隔
                    headerRow.push('');
                    headerRow.push('');
                    // 添加统计数据标题
                    if (statsData[0] && statsData[0][0]) {
                        headerRow.push(statsData[0][0]);
                    } else {
                        headerRow.push('统计数据');
                    }
                    // 填充剩余列
                    for (let i = headerRow.length; i < totalColumns; i++) headerRow.push('');
                    aoa.push(headerRow);
                    
                    // 数据行
                    const maxRows = Math.max(tableRows.length, statsData.length - 1);
                    
                    for (let i = 0; i < maxRows; i++) {
                        const row = [];
                        
                        // 销售明细部分
                        if (i < tableRows.length) {
                            row.push(...tableRows[i]);
                        } else {
                            row.push(...Array(tableColCount).fill(''));
                        }
                        
                        // 分隔列
                        row.push('');
                        row.push('');
                        
                        // 统计数据部分 (从索引1开始，因为0是标题)
                        if (i + 1 < statsData.length) {
                            row.push(...statsData[i + 1]);
                        } else {
                            row.push(...Array(statsColCount).fill(''));
                        }
                        
                        // 确保所有行的长度一致
                        while (row.length < totalColumns) row.push('');
                        
                        aoa.push(row);
                    }
                    
                    // 创建工作表
                    const worksheet = XLSX.utils.aoa_to_sheet(aoa);
                    
                    // 合并标题单元格
                    worksheet['!merges'] = [
                        // 标题行合并
                        {s: {r: 0, c: 0}, e: {r: 0, c: totalColumns - 1}}
                    ];
                    
                    // 设置列宽
                    const colWidths = [];
                    
                    // 销售明细部分列宽
                    for (let i = 0; i < tableColCount; i++) {
                        colWidths.push({wch: 15});
                    }
                    
                    // 分隔列
                    colWidths.push({wch: 3});
                    colWidths.push({wch: 3});
                    
                    // 统计数据部分列宽
                    for (let i = 0; i < statsColCount; i++) {
                        colWidths.push({wch: 12});
                    }
                    
                    worksheet['!cols'] = colWidths;
                    
                    // 添加工作表到工作簿
                    XLSX.utils.book_append_sheet(wb, worksheet, currentFileName + " 数据");
                    
                    // 下载Excel文件
                    const fileName = currentFileName + '_数据_' + new Date().toISOString().split('T')[0] + '.xlsx';
                    XLSX.writeFile(wb, fileName);
                    
                    alert('Excel文件导出成功!');
                } catch (error) {
                    console.error('导出Excel失败:', error);
                    alert('导出Excel失败: ' + error.message);
                }
            }
        });
    </script>
</body>
</html>
'''

def parse_time(time_str):
    return InventoryProcessor.parse_time(time_str)

def read_file(file_path):
    if not os.path.isfile(file_path):
        # 如果文件不存在，尝试在当前目录查找
        current_dir = os.getcwd()
        base_filename = os.path.basename(file_path)
        alternative_path = os.path.join(current_dir, base_filename)

        if os.path.isfile(alternative_path):
            file_path = alternative_path
        else:
            logging.warning(f"文件不存在: {file_path}")
            return []
    try:
        file_mtime = os.path.getmtime(file_path)
        cache_key = (file_path, file_mtime)

        # 使用缓存数据，如果存在
        if cache_key in read_file.cache:
            return read_file.cache[cache_key]

        with open(file_path, 'r') as file:
            lines = [line.strip() for line in file if line.strip()]

            # 优化排序 - 只在必要时进行排序
            if lines and ',' in lines[0]:  # 确保文件格式正确
                lines.sort(key=lambda x: parse_time(x.split(',')[0]))

            # 维护缓存大小，防止内存泄漏
            if len(read_file.cache) > 20:  # 限制缓存条目数量
                # 删除最旧的缓存项
                oldest_key = next(iter(read_file.cache))
                del read_file.cache[oldest_key]

            read_file.cache[cache_key] = lines
            return lines
    except Exception as e:
        logging.error(f"文件读取失败: {e}")
        return []

# 初始化缓存字典
read_file.cache = {}

# 添加结果缓存字典
process_request_cache = {}

def process_request(buttons, default_file, highlight_num_override=None, start=0, end=None):
    # 检测是否为AJAX请求
    is_ajax = request.headers.get('X-Requested-With') == 'XMLHttpRequest'

    # 构建缓存键
    cache_key = (
        tuple(buttons),
        default_file,
        highlight_num_override,
        start,
        end,
        request.form.get('threshold'),
        request.form.get('highlight_num'),
        request.form.get('cut_value'),
        is_ajax  # 添加AJAX状态到缓存键
    )

    # 检查是否有表单提交操作，这会导致状态变化
    form_submitted = False
    for key in buttons:
        if key in request.form:
            form_submitted = True
            break

    # 检查是否有表单设置提交
    settings_submitted = 'threshold' in request.form or 'highlight_num' in request.form or 'cut_value' in request.form

    # 如果没有表单提交且缓存中有结果，直接返回缓存结果
    if not form_submitted and not settings_submitted and cache_key in process_request_cache:
        return process_request_cache[cache_key]

    selected_button = None
    for key in buttons:
        if key in request.form:
            selected_button = key
            break

    # 获取当前可用的文件
    file_paths = get_available_files()

    # 只有在点击按钮时更改当前文件
    if selected_button and selected_button in file_paths:
        session['current_file'] = file_paths[selected_button]
    # 如果是首次访问该路由，设置默认文件
    elif 'current_file' not in session:
        session['current_file'] = default_file
    # 否则保持当前文件不变

    file_to_show = session['current_file']
    data = read_file(file_to_show)
    is_ph = 'ph' in file_to_show.lower()
    is_ma = any(kw in file_to_show.lower() for kw in ['ma', 'mark'])
    is_moon ='moon' in file_to_show.lower()
    # 获取当前显示的文件名
    file_name = next((k for k, v in file_paths.items() if v == file_to_show), '')
    # 如果在URL中没有找到匹配的键，使用文件路径的基本名称
    if not file_name:
        file_name = os.path.basename(file_to_show).split('.')[0]

    try:
        threshold = int(request.form.get('threshold')) if request.form.get('threshold') else None
    except:
        threshold = None

    # 特定文件的高光数量设置
    if file_name in ['kickflip', 'ahof']:
        default_highlight_num = 150  # jack的高光个数已设置为150个
    elif file_name in ['sakuya', 'riku', 'yushi', 'jaehee', 'ryo', 'sion']:
        default_highlight_num = 40
    elif file_name in ['smn','allen','hyeongjun','jungmo','minhee','seongmin','serim','taeyoung','wonjin','woobin']:  # photo高光设置10个
        default_highlight_num = 10
    elif file_name in ['yy','kun','xj','ten','hen','renjun','jisung','jaemin','jeno','mark','haechan','chenle','closeyoureye','coffee','soobin', 'yeonjun', 'beomgyu', 'taehyun', 'huenigkai','seulgi','irene','alldayproject']:
        default_highlight_num = 30
    elif file_name == 'face':  # gidle高光设置为250个
        default_highlight_num = 70
    elif file_name in ['签名应募','stayc']: # shjack的高光个数设置为150个
        default_highlight_num = 200
    elif file_name  in ['yenasz', 'yenagz']:  # ive的高光个数设置为200个
        default_highlight_num = 100
    elif file_name in ['合影应募','soyeon', 'yuqi', 'shuhua', 'miyeon', 'minnie']:
        default_highlight_num = 10
    elif file_name in ['anton', 'eunseok', 'shotaro', 'sohee', 'sungchan', 'wonbin']:
        default_highlight_num = 40  # 修改为30个
    elif 'ph' in file_name:  # 含ph的高光个数设置为10个
        default_highlight_num = 10
    else:
        default_highlight_num = 225

    if highlight_num_override is not None:
        highlight_num = highlight_num_override
    elif request.form.get('highlight_num'):
        try:
            highlight_num = int(request.form['highlight_num'])
        except ValueError:
            highlight_num = default_highlight_num
    else:
        highlight_num = default_highlight_num

    if file_name =='':
        highlight_num = 1016
    elif file_name == '':
        highlight_num = 593

    # 调整最大高光数量限制
    if file_name == 'doyoung':
        max_highlight = 200
    elif file_name in []:
        max_highlight = 20
    else:
        max_highlight = 260

    highlight_num = min(highlight_num, max_highlight)

    processor = InventoryProcessor(
        threshold=threshold,
        highlight_num=highlight_num,
        is_ph_file=is_ph,
        is_ma=is_ma
    )

    processed, highlighted, total, kms_total = processor.process_data(data)

    # 处理特定文件的总销量调整
    if file_name == '签名应募':
        total += 419  # photo总销量加上1880
    elif file_name == '合影签售':
        total += 1208  # sign总销量加上4096
    elif file_name == 'coffee':
        total += 413   # jack总销量加上269
    elif file_name == 'kissoflife':
        total += 187   # nexz总销量加上319
    elif file_name == 'le':
        total += 483
    highlighted = highlighted if highlighted else []
    stats = InventoryProcessor.calculate_stats(highlighted)

    # Initialize all stats variables as None
    stats_176_275 = None
    stats_1_25 = None
    stats_26_40 = None
    stats_41_50 = None  # 新增41-50区间统计
    stats_1_50 = None
    stats_51_250 = None
    stats_51_200 = None
    stats_51_190 = None
    stats_51_150 = None
    stats_61_100 = None
    stats_101_140 = None
    stats_101_150 = None
    stats_61_140 = None
    stats_141_240 = None
    stats_26_50 = None
    stats_1_5 = None
    stats_1_15 = None
    stats_16_30 = None
    stats_160_220 = None
    stats_51_100 = None
    stats_1_20 = None
    stats_21_30 = None
    stats_151_250 = None
    stats_51_125 = None
    stats_126_200 = None
    stats_26_35 = None
    stats_1_6 = None  # 新增1-6区间统计
    stats_7_12 = None  # 新增7-12区间统计
    # 新增区间统计
    stats_1_125 = None  # wayv 1-125
    stats_126_225 = None  # wayv 126-225
    stats_6_10 = None  # soyeon 6-10
    stats_1_60 = None  # superjunior 1-60
    stats_61_260 = None  # superjunior 61-260
    stats_1_10 = None  # cravity 1-10
    stats_15_20 = None  # cravity 15-20

    # Calculate stats based on file_name
    if file_name == 'all':
        values_176_275 = highlighted[175:275]
        stats_176_275 = InventoryProcessor.calculate_stats(values_176_275)
    elif file_name in ['yy','kun','xj','ten','hen','renjun','jisung','jaemin','jeno','mark','haechan','chenle','coffee','soobin', 'yeonjun', 'beomgyu', 'taehyun', 'huenigkai','seulgi','irene','alldayproject','closeyoureye']:
        values_1_25 = highlighted[:15]
        stats_1_25 = InventoryProcessor.calculate_stats(values_1_25)
        values_26_40 = highlighted[15:30]
        stats_26_40 = InventoryProcessor.calculate_stats(values_26_40)
    elif file_name == 'ive':
        values_1_50 = highlighted[:50]
        stats_1_50 = InventoryProcessor.calculate_stats(values_1_50)
        values_51_200 = highlighted[50:200]
        stats_51_200 = InventoryProcessor.calculate_stats(values_51_200)
        values_51_125 = highlighted[50:125]
        stats_51_125 = InventoryProcessor.calculate_stats(values_51_125)
        values_126_200 = highlighted[125:200]
        stats_126_200 = InventoryProcessor.calculate_stats(values_126_200)
    elif file_name in ['single', '']: # 添加riize的分段统计
        values_1_50 = highlighted[:40]
        stats_1_50 = InventoryProcessor.calculate_stats(values_1_50)
        values_51_190 = highlighted[40:80]
        stats_51_190 = InventoryProcessor.calculate_stats(values_51_190)
    elif file_name in ['kickflip', 'ahof']: # 新增sign分段统计
        values_1_50 = highlighted[:50]
        stats_1_50 = InventoryProcessor.calculate_stats(values_1_50)
        values_51_200 = highlighted[50:150]
        stats_51_200 = InventoryProcessor.calculate_stats(values_51_200)
    elif file_name == '1':  # 修改gidle分段统计为1-50, 51-250
        values_1_50 = highlighted[:50]
        stats_1_50 = InventoryProcessor.calculate_stats(values_1_50)
        values_51_250 = highlighted[50:250]
        stats_51_250 = InventoryProcessor.calculate_stats(values_51_250)
        values_51_150 = highlighted[50:150]
        stats_51_150 = InventoryProcessor.calculate_stats(values_51_150)
        values_151_250 = highlighted[150:250]
        stats_151_250 = InventoryProcessor.calculate_stats(values_151_250)
    elif file_name == 'face':  # 新增shjack分段统计
        values_1_50 = highlighted[:35]
        stats_1_50 = InventoryProcessor.calculate_stats(values_1_50)
        values_51_200 = highlighted[35:70]
        stats_51_200 = InventoryProcessor.calculate_stats(values_51_200)
    elif file_name in ['single']:
        values_1_25 = highlighted[:40]
        stats_1_25 = InventoryProcessor.calculate_stats(values_1_25)
        values_26_50 = highlighted[40:80]
        stats_26_50 = InventoryProcessor.calculate_stats(values_26_50)
    elif file_name in ['sakuya', 'riku', 'yushi', 'jaehee', 'ryo', 'sion']:
        # 修改为1-25, 26-40, 41-50区间统计
        values_1_25 = highlighted[:min(15, len(highlighted))]
        stats_1_25 = InventoryProcessor.calculate_stats(values_1_25)
        values_26_40 = highlighted[15:min(30, len(highlighted))]
        stats_26_40 = InventoryProcessor.calculate_stats(values_26_40)
        values_41_50 = highlighted[40:min(50, len(highlighted))]
        stats_41_50 = InventoryProcessor.calculate_stats(values_41_50)
    elif 'ph' in file_name:
        # 修改含ph文件为1-6, 7-12区间统计
        values_1_6 = highlighted[:min(6, len(highlighted))]
        stats_1_6 = InventoryProcessor.calculate_stats(values_1_6)
        values_7_12 = highlighted[6:min(12, len(highlighted))]
        stats_7_12 = InventoryProcessor.calculate_stats(values_7_12)
    elif file_name in ['anton', 'eunseok', 'shotaro', 'sohee', 'sungchan', 'wonbin']:
        # 新增这些文件的1-15和16-30区间统计
        values_1_15 = highlighted[:min(20, len(highlighted))]
        stats_1_15 = InventoryProcessor.calculate_stats(values_1_15)
        values_16_30 = highlighted[20:min(40, len(highlighted))]
        stats_16_30 = InventoryProcessor.calculate_stats(values_16_30)
    elif file_name == 'wayv':
        values_1_125 = highlighted[:125]
        stats_1_125 = InventoryProcessor.calculate_stats(values_1_125)
        values_126_225 = highlighted[125:225]
        stats_126_225 = InventoryProcessor.calculate_stats(values_126_225)
    elif file_name in ['yenasz', 'yenagz']:
        values_1_5 = highlighted[:30]
        stats_1_5 = InventoryProcessor.calculate_stats(values_1_5)
        values_6_10 = highlighted[30:100]
        stats_6_10 = InventoryProcessor.calculate_stats(values_6_10)
    elif file_name in ['stayc','签名应募']:
        values_1_60 = highlighted[:50]
        stats_1_60 = InventoryProcessor.calculate_stats(values_1_60)
        values_61_260 = highlighted[50:200]
        stats_61_260 = InventoryProcessor.calculate_stats(values_61_260)
    elif file_name in ['合影应募','smn','allen', 'hyeongjun', 'jungmo', 'minhee', 'seongmin', 'serim', 'taeyoung', 'wonjin', 'woobin']:
        values_1_10 = highlighted[:5]
        stats_1_10 = InventoryProcessor.calculate_stats(values_1_10)
        values_15_20 = highlighted[5:10]
        stats_15_20 = InventoryProcessor.calculate_stats(values_15_20)
    cut = None
    if 'cut_value' in request.form:
        try:
            divisor = float(request.form['cut_value'])
            cut = total / divisor if divisor != 0 else "除零错误"
        except:
            cut = "无效输入"

    # 准备渲染结果
    result = render_template_string(template,
                                    show_buttons=True,
                                    buttons=buttons,
                                    data=processed,
                                    highlighted_values=highlighted,
                                    stats=stats,
                                    total=int(total),
                                    kms_total=int(kms_total),
                                    highlight_num=highlight_num,
                                    cut=cut,
                                    file_name=file_name,
                                    stats_176_275=stats_176_275,
                                    stats_1_25=stats_1_25,
                                    stats_26_40=stats_26_40,
                                    stats_41_50=stats_41_50,  # 添加新的统计变量
                                    stats_1_50=stats_1_50,
                                    stats_51_250=stats_51_250,
                                    stats_51_200=stats_51_200,
                                    stats_51_190=stats_51_190,
                                    stats_51_150=stats_51_150,
                                    stats_61_100=stats_61_100,
                                    stats_101_140=stats_101_140,
                                    stats_61_140=stats_61_140,
                                    stats_141_240=stats_141_240,
                                    stats_26_50=stats_26_50,
                                    stats_51_100=stats_51_100,
                                    stats_160_220=stats_160_220,
                                    stats_1_5=stats_1_5,
                                    stats_1_15=stats_1_15,
                                    stats_16_30=stats_16_30,
                                    stats_1_20=stats_1_20,
                                    stats_21_30=stats_21_30,
                                    stats_151_250=stats_151_250,
                                    stats_51_125=stats_51_125,
                                    stats_126_200=stats_126_200,
                                    stats_26_35=stats_26_35,
                                    stats_1_6=stats_1_6,  # 添加新的统计变量
                                    stats_7_12=stats_7_12,  # 添加新的统计变量
                                    stats_1_125=stats_1_125,  # 添加新的统计变量
                                    stats_126_225=stats_126_225,  # 添加新的统计变量
                                    stats_6_10=stats_6_10,  # 添加新的统计变量
                                    stats_1_60=stats_1_60,  # 添加新的统计变量
                                    stats_61_260=stats_61_260,  # 添加新的统计变量
                                    stats_1_10=stats_1_10,
                                    stats_15_20=stats_15_20)  # 添加新的统计变量

    # 缓存结果，仅在表单提交时不缓存
    if not form_submitted:
        # 维护缓存大小，防止内存溢出
        if len(process_request_cache) > 30:  # 限制缓存条目数量
            # 删除一个随机键以避免同时删除所有缓存
            random_key = next(iter(process_request_cache))
            del process_request_cache[random_key]
        process_request_cache[cache_key] = result

    return result



@app.route('/tb02', methods=['GET', 'POST'])
def tb02_data():
    buttons = get_available_buttons()
    return process_request(buttons, 'sion.txt')

# @app.route('/phdonghai', methods=['GET', 'POST'])
# def phdonghai_data():
#     return process_request(['phdonghai'], 'phdonghai.txt')
#
#
# @app.route('/sion42', methods=['GET', 'POST'])
# def sion_data():
#     return process_request(['sion'], 'sion.txt')
#
# @app.route('/phsion37', methods=['GET', 'POST'])
# def phsion_data():
#     return process_request(['phsion'], 'phsion.txt')
#
# @app.route('/riku19', methods=['GET', 'POST'])
# def riku_data():
#     return process_request(['riku'], 'riku.txt')
#
# @app.route('/phriku56', methods=['GET', 'POST'])
# def phriku_data():
#     return process_request(['phriku'], 'phriku.txt')
#
# @app.route('/yushi28', methods=['GET', 'POST'])
# def yushi_data():
#     return process_request(['yushi'], 'yushi.txt')
#
# @app.route('/phyushi71', methods=['GET', 'POST'])
# def phyushi_data():
#     return process_request(['phyushi'], 'phyushi.txt')
#
# @app.route('/jaehee45', methods=['GET', 'POST'])
# def jaehee_data():
#     return process_request(['jaehee'], 'jaehee.txt')
#
# @app.route('/phjaehee63', methods=['GET', 'POST'])
# def phjaehee_data():
#     return process_request(['phjaehee'], 'phjaehee.txt')
#
# @app.route('/ryo22', methods=['GET', 'POST'])
# def ryo_data():
#     return process_request(['ryo'], 'ryo.txt')
#
# @app.route('/phryo39', methods=['GET', 'POST'])
# def phryo_data():
#     return process_request(['phryo'], 'phryo.txt')
#
# @app.route('/sakuya84', methods=['GET', 'POST'])
# def sakuya_data():
#     return process_request(['sakuya'], 'sakuya.txt')
#
# @app.route('/phsakuya51', methods=['GET', 'POST'])
# def phsakuya_data():
#     return process_request(['phsakuya'], 'phsakuya.txt')
#
# @app.route('/allsion76', methods=['GET', 'POST'])
# def allsion_data():
#     return process_request(['sion', 'phsion'], 'allsion.txt')
#
# @app.route('/allriku49', methods=['GET', 'POST'])
# def allriku_data():
#     return process_request(['riku', 'phriku'], 'allriku.txt')
#
# @app.route('/allyushi33', methods=['GET', 'POST'])
# def allyushi_data():
#     return process_request(['yushi', 'phyushi'], 'allyushi.txt')
#
# @app.route('/alljaehee68', methods=['GET', 'POST'])
# def alljaehee_data():
#     return process_request(['jaehee', 'phjaehee'], 'alljaehee.txt')
#
# @app.route('/allryo27', methods=['GET', 'POST'])
# def allryo_data():
#     return process_request(['ryo', 'phryo'], 'allryo.txt')
#
# @app.route('/allsakuya58', methods=['GET', 'POST'])
# def allsakuya_data():
#     return process_request(['sakuya', 'phsakuya'], 'allsakuya.txt')
#
# @app.route('/alldayproject15', methods=['GET', 'POST'])
# def alldayproject_data():
#     return process_request(['alldayproject'], 'alldayproject.txt')
#
# @app.route('/irene11', methods=['GET', 'POST'])
# def irene_data():
#     return process_request(['irene'], 'irene.txt')
#
# @app.route('/seulgi33', methods=['GET', 'POST'])
# def seulgi_data():
#     return process_request(['seulgi'], 'seulgi.txt')
#
@app.route('/taehyun33', methods=['GET', 'POST'])
def taehyun_data():
    if request.method == 'GET':
        session['current_file'] = 'taehyun.txt'  # Explicitly set default file for this route
    return process_request(['taehyun'], 'taehyun.txt')




@app.route('/beomgyu32', methods=['GET', 'POST'])
def beomgyu_data():
    if request.method == 'GET':
        session['current_file'] = 'beomgyu.txt'  # Explicitly set default file for this route
    return process_request(['beomgyu'], 'beomgyu.txt')


@app.route('/yeonjun12', methods=['GET', 'POST'])
def yeonjun_data():
    if request.method == 'GET':
        session['current_file'] = 'yeonjun.txt'  # Explicitly set default file for this route
    return process_request(['yeonjun'], 'yeonjun.txt')


@app.route('/soobin21', methods=['GET', 'POST'])
def soobin_data():
    if request.method == 'GET':
        session['current_file'] = 'soobin.txt'  # Explicitly set default file for this route
    return process_request(['soobin'], 'soobin.txt')

@app.route('/huenigkai45', methods=['GET', 'POST'])
def allhaechan_data():
    if request.method == 'GET':
        session['current_file'] = 'huenigkai.txt'  # Explicitly set default file for this route
    return process_request(['huenigkai'], 'huenigkai.txt')

@app.route('/closeyoureye22', methods=['GET', 'POST'])
def closeyoureye_data():
    if request.method == 'GET':
        session['current_file'] = 'closeyoureye.txt'  # Explicitly set default file for this route
    return process_request(['closeyoureye'], 'closeyoureye.txt')




@app.route('/txt5', methods=['GET', 'POST'])
def txt_data():
    if request.method == 'GET':
        session['current_file'] = 'soobin.txt'  # Explicitly set default file for this route
    return process_request(['soobin','yeonjun','beomgyu','taehyun','huenigkai'], 'soobin.txt')


# @app.route('/sleep2', methods=['GET', 'POST'])
# def sleep_data():
#     return process_request(['yeonjun','beomgyu'], 'yeonjun.txt')
#
# @app.route('/kickflip11', methods=['GET', 'POST'])
# def kickflip_data():
#     return process_request(['kickflip'], 'kickflip.txt')
#
# @app.route('/face11', methods=['GET', 'POST'])
# def face_data():
#     return process_request(['face'], 'face.txt')
#
# @app.route('/coffee99', methods=['GET', 'POST'])
# def coffee_data():
#     return process_request(['coffee'], 'coffee.txt')
#
# @app.route('/txt14', methods=['GET', 'POST'])
# def txt_data():
#     return process_request(['yeonjun','taehyun','huenigkai'], 'yeonjun.txt')

# @app.route('/stayc12', methods=['GET', 'POST'])
# def stayc_data():
#     return process_request(['stayc'], 'stayc.txt')
# #
# @app.route('/closeyoureye15', methods=['GET', 'POST'])
# def closeyoureye_data():
#     return process_request(['closeyoureye'], 'closeyoureye.txt')

# @app.route('/dang5', methods=['GET', 'POST'])
# def dang_data():
#     return process_request(['soobin','yeonjun','beomgyu','taehyun','huenigkai'], 'soobin.txt')
#
#
# @app.route('/fan', methods=['GET', 'POST'])
# def fan_data():
#     return process_request(['kickflip','face','coffee'], 'kickflip.txt')


@app.route('/kun32', methods=['GET', 'POST'])
def kun_data():
    if request.method == 'GET':
        session['current_file'] = 'kun.txt'  # Explicitly set default file for this route
    return process_request(['kun'], 'kun.txt')

@app.route('/phkun43', methods=['GET', 'POST'])
def phkun_data():
    if request.method == 'GET':
        session['current_file'] = 'phkun.txt'  # Explicitly set default file for this route
    return process_request(['phkun'], 'phkun.txt')

@app.route('/ten53', methods=['GET', 'POST'])
def ten_data():
    if request.method == 'GET':
        session['current_file'] = 'ten.txt'  # Explicitly set default file for this route
    return process_request(['ten'], 'ten.txt')

@app.route('/phten21', methods=['GET', 'POST'])
def phten_data():
    if request.method == 'GET':
        session['current_file'] = 'phten.txt'  # Explicitly set default file for this route
    return process_request(['phten'], 'phten.txt')

@app.route('/xj52', methods=['GET', 'POST'])
def xj_data():
    if request.method == 'GET':
        session['current_file'] = 'xj.txt'  # Explicitly set default file for this route
    return process_request(['xj'], 'xj.txt')

@app.route('/phxj31', methods=['GET', 'POST'])
def phxj_data():
    if request.method == 'GET':
        session['current_file'] = 'phxj.txt'  # Explicitly set default file for this route
    return process_request(['phxj'], 'phxj.txt')

@app.route('/yy66', methods=['GET', 'POST'])
def yy_data():
    if request.method == 'GET':
        session['current_file'] = 'yy.txt'  # Explicitly set default file for this route
    return process_request(['yy'], 'yy.txt')

@app.route('/phyy61', methods=['GET', 'POST'])
def phyy_data():
    if request.method == 'GET':
        session['current_file'] = 'phyy.txt'  # Explicitly set default file for this route
    return process_request(['phyy'], 'phyy.txt')

@app.route('/allhen22', methods=['GET', 'POST'])
def allhen_data():
    if request.method == 'GET':
        session['current_file'] = 'hen.txt'  # Explicitly set default file for this route
    return process_request(['hen', 'phhen'], 'hen.txt')

@app.route('/allkun21', methods=['GET', 'POST'])
def allkun_data():
    if request.method == 'GET':
        session['current_file'] = 'kun.txt'  # Explicitly set default file for this route
    return process_request(['kun', 'phkun'], 'kun.txt')

@app.route('/allten55', methods=['GET', 'POST'])
def allten_data():
    if request.method == 'GET':
        session['current_file'] = 'ten.txt'  # Explicitly set default file for this route
    return process_request(['ten', 'phten'], 'ten.txt')

@app.route('/allxj34', methods=['GET', 'POST'])
def allxj_data():
    if request.method == 'GET':
        session['current_file'] = 'xj.txt'  # Explicitly set default file for this route
    return process_request(['xj', 'phxj'], 'xj.txt')

@app.route('/allyy51', methods=['GET', 'POST'])
def allyy_data():
    if request.method == 'GET':
        session['current_file'] = 'yy.txt'  # Explicitly set default file for this route
    return process_request(['yy', 'phyy'], 'yy.txt')

@app.route('/wayv11', methods=['GET', 'POST'])
def wayv_data():
    if request.method == 'GET':
        session['current_file'] = 'wayv.txt'  # Explicitly set default file for this route
    return process_request(['wayv'], 'wayv.txt')

@app.route('/way11', methods=['GET', 'POST'])
def way_data():
    if request.method == 'GET':
        session['current_file'] = 'yy.txt'  # Explicitly set default file for this route
    return process_request(['wayv','kun','phkun','yy','phyy','ten','phten','hen','phhen','xj','phxj'], 'yy.txt')


@app.route('/phsakuya', methods=['GET', 'POST'])
def phsakuya_data():
    if request.method == 'GET':
        session['current_file'] = 'phsakuya.txt'  # Explicitly set default file for this route
    return process_request(['phsakuya'], 'phsakuya.txt')


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=1188, debug=True)